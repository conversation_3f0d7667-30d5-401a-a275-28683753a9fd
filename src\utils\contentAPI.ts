/**
 * Production-Ready Content API
 * Handles all database operations for content management
 */

import { MediaItem } from '@/types/media';
import { ContentFormData } from '@/types/admin';

// API Configuration
const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3001/api';
const IS_PRODUCTION = process.env.NODE_ENV === 'production' || !import.meta.env.DEV;

/**
 * Generic API request handler with error handling
 */
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Content API service
 */
export const contentAPI = {
  /**
   * Create new content
   */
  async createContent(formData: ContentFormData): Promise<{ success: boolean; id?: string; message: string }> {
    try {
      // Transform form data to API format
      const contentData = {
        title: formData.title,
        description: formData.description,
        year: parseInt(formData.year) || new Date().getFullYear(),
        type: formData.type,
        category_id: formData.category,
        image: formData.posterUrl,
        cover_image: formData.thumbnailUrl,
        tmdb_id: formData.tmdbId ? parseInt(formData.tmdbId) : null,
        poster_url: formData.posterUrl,
        thumbnail_url: formData.thumbnailUrl,
        video_links: formData.videoLinks,
        secure_video_links: formData.secureVideoLinks,
        imdb_rating: formData.imdbRating ? parseFloat(formData.imdbRating) : null,
        runtime: formData.runtime ? parseInt(formData.runtime) : null,
        studio: formData.studio,
        tags: formData.tags,
        trailer: formData.trailer,
        subtitle_url: formData.subtitleUrl,
        is_published: formData.isPublished,
        is_featured: formData.isFeatured,
        add_to_carousel: formData.addToCarousel,
        genres: formData.genres,
        languages: formData.languages,
        quality: formData.quality,
        audioTracks: formData.audioTracks,
      };

      const result = await apiRequest<{ success: boolean; id: string; message: string }>('/content', {
        method: 'POST',
        body: JSON.stringify(contentData),
      });

      return result;
    } catch (error) {
      console.error('Failed to create content:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create content',
      };
    }
  },

  /**
   * Update existing content
   */
  async updateContent(id: string, formData: ContentFormData): Promise<{ success: boolean; message: string }> {
    try {
      const contentData = {
        title: formData.title,
        description: formData.description,
        year: parseInt(formData.year) || new Date().getFullYear(),
        type: formData.type,
        category_id: formData.category,
        image: formData.posterUrl,
        cover_image: formData.thumbnailUrl,
        tmdb_id: formData.tmdbId ? parseInt(formData.tmdbId) : null,
        poster_url: formData.posterUrl,
        thumbnail_url: formData.thumbnailUrl,
        video_links: formData.videoLinks,
        secure_video_links: formData.secureVideoLinks,
        imdb_rating: formData.imdbRating ? parseFloat(formData.imdbRating) : null,
        runtime: formData.runtime ? parseInt(formData.runtime) : null,
        studio: formData.studio,
        tags: formData.tags,
        trailer: formData.trailer,
        subtitle_url: formData.subtitleUrl,
        is_published: formData.isPublished,
        is_featured: formData.isFeatured,
        add_to_carousel: formData.addToCarousel,
        genres: formData.genres,
        languages: formData.languages,
        quality: formData.quality,
        audioTracks: formData.audioTracks,
      };

      const result = await apiRequest<{ success: boolean; message: string }>(`/content/${id}`, {
        method: 'PUT',
        body: JSON.stringify(contentData),
      });

      return result;
    } catch (error) {
      console.error('Failed to update content:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update content',
      };
    }
  },

  /**
   * Get all content with pagination and filters
   */
  async getContent(params: {
    page?: number;
    limit?: number;
    type?: string;
    category?: string;
    search?: string;
    published?: boolean;
  } = {}): Promise<{ success: boolean; data: MediaItem[]; total: number; message?: string }> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.type) queryParams.append('type', params.type);
      if (params.category) queryParams.append('category', params.category);
      if (params.search) queryParams.append('search', params.search);
      if (params.published !== undefined) queryParams.append('published', params.published.toString());

      const result = await apiRequest<{ success: boolean; data: MediaItem[]; total: number }>(`/content?${queryParams}`);
      return result;
    } catch (error) {
      console.error('Failed to fetch content:', error);
      return {
        success: false,
        data: [],
        total: 0,
        message: error instanceof Error ? error.message : 'Failed to fetch content',
      };
    }
  },

  /**
   * Get single content item by ID
   */
  async getContentById(id: string): Promise<{ success: boolean; data?: MediaItem; message?: string }> {
    try {
      const result = await apiRequest<{ success: boolean; data: MediaItem }>(`/content/${id}`);
      return result;
    } catch (error) {
      console.error('Failed to fetch content by ID:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch content',
      };
    }
  },

  /**
   * Delete content
   */
  async deleteContent(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const result = await apiRequest<{ success: boolean; message: string }>(`/content/${id}`, {
        method: 'DELETE',
      });

      return result;
    } catch (error) {
      console.error('Failed to delete content:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete content',
      };
    }
  },

  /**
   * Bulk delete content
   */
  async bulkDeleteContent(ids: string[]): Promise<{ success: boolean; message: string; deleted: number }> {
    try {
      const result = await apiRequest<{ success: boolean; message: string; deleted: number }>('/content/bulk-delete', {
        method: 'POST',
        body: JSON.stringify({ ids }),
      });

      return result;
    } catch (error) {
      console.error('Failed to bulk delete content:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete content',
        deleted: 0,
      };
    }
  },

  /**
   * Bulk create content (CSV/JSON import)
   */
  async bulkCreateContent(contentItems: ContentFormData[]): Promise<{ 
    success: boolean; 
    message: string; 
    created: number; 
    failed: number; 
    errors?: string[] 
  }> {
    try {
      const result = await apiRequest<{ 
        success: boolean; 
        message: string; 
        created: number; 
        failed: number; 
        errors?: string[] 
      }>('/content/bulk-create', {
        method: 'POST',
        body: JSON.stringify({ items: contentItems }),
      });

      return result;
    } catch (error) {
      console.error('Failed to bulk create content:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create content',
        created: 0,
        failed: contentItems.length,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  },

  /**
   * Get content statistics
   */
  async getContentStats(): Promise<{
    success: boolean;
    data?: {
      total: number;
      published: number;
      featured: number;
      movies: number;
      series: number;
      byCategory: Record<string, number>;
    };
    message?: string;
  }> {
    try {
      const result = await apiRequest<{
        success: boolean;
        data: {
          total: number;
          published: number;
          featured: number;
          movies: number;
          series: number;
          byCategory: Record<string, number>;
        };
      }>('/content/stats');

      return result;
    } catch (error) {
      console.error('Failed to fetch content stats:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch statistics',
      };
    }
  },
};

/**
 * Development fallback for when database is unavailable
 */
export const mockContentAPI = {
  async createContent(formData: ContentFormData) {
    console.warn('Using mock API - content not actually saved to database');
    console.log('Mock content creation:', formData);
    return {
      success: true,
      id: `mock-${Date.now()}`,
      message: 'Content created successfully (mock mode)',
    };
  },

  async updateContent(id: string, formData: ContentFormData) {
    console.warn('Using mock API - content not actually updated in database');
    console.log('Mock content update:', { id, formData });
    return {
      success: true,
      message: 'Content updated successfully (mock mode)',
    };
  },

  async getContent() {
    console.warn('Using mock API - returning empty content list');
    return {
      success: true,
      data: [],
      total: 0,
      message: 'Mock mode - no content available',
    };
  },

  async deleteContent(id: string) {
    console.warn('Using mock API - content not actually deleted from database');
    console.log('Mock content deletion:', id);
    return {
      success: true,
      message: 'Content deleted successfully (mock mode)',
    };
  },
};

/**
 * Get the appropriate API based on environment and availability
 */
export function getContentAPI() {
  if (IS_PRODUCTION) {
    return contentAPI;
  } else {
    // In development, try real API first, fallback to mock
    return contentAPI; // We'll handle fallback in the individual methods
  }
}
