const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { LoginAttemptsService, SecurityLogsService, AuthTokensService } = require('../services/storageService');

const router = express.Router();

// Login validation rules
const loginValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
];

// Register validation rules (for creating new admin users)
const registerValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('role')
    .optional()
    .isIn(['admin', 'moderator'])
    .withMessage('Role must be either admin or moderator')
];

// Login endpoint
router.post('/login', loginValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { username, password } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Check if user exists and is active
    const userQuery = 'SELECT * FROM admin_users WHERE username = ? AND is_active = 1';
    const [user] = await db.execute(userQuery, [username]);

    if (!user) {
      // Log failed attempt
      await logSecurityEvent(null, 'login_failed', clientIP, userAgent, {
        username,
        reason: 'user_not_found'
      });

      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Invalid username or password'
      });
    }

    // Check if account is locked
    if (user.locked_until && new Date() < new Date(user.locked_until)) {
      await logSecurityEvent(user.id, 'login_blocked', clientIP, userAgent, {
        reason: 'account_locked',
        locked_until: user.locked_until
      });

      return res.status(423).json({
        error: 'Account Locked',
        message: 'Account is temporarily locked due to too many failed attempts'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
      // Increment failed attempts
      const failedAttempts = (user.failed_login_attempts || 0) + 1;
      const lockUntil = failedAttempts >= 5 
        ? new Date(Date.now() + 15 * 60 * 1000) // Lock for 15 minutes
        : null;

      await db.execute(
        'UPDATE admin_users SET failed_login_attempts = ?, locked_until = ? WHERE id = ?',
        [failedAttempts, lockUntil, user.id]
      );

      await logSecurityEvent(user.id, 'login_failed', clientIP, userAgent, {
        username,
        failed_attempts: failedAttempts,
        reason: 'invalid_password'
      });

      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Invalid username or password'
      });
    }

    // Reset failed attempts on successful login
    await db.execute(
      'UPDATE admin_users SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?',
      [user.id]
    );

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username, 
        role: user.role 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Create session
    req.session.userId = user.id;
    req.session.username = user.username;
    req.session.role = user.role;

    // Log successful login
    await logSecurityEvent(user.id, 'login_success', clientIP, userAgent, {
      username: user.username
    });

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred during login'
    });
  }
});

// Logout endpoint
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Log logout
    await logSecurityEvent(req.user.userId, 'logout', clientIP, userAgent, {
      username: req.user.username
    });

    // Destroy session
    req.session.destroy((err) => {
      if (err) {
        console.error('Session destruction error:', err);
      }
    });

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred during logout'
    });
  }
});

// Verify token endpoint
router.get('/verify', authenticateToken, async (req, res) => {
  try {
    const userQuery = 'SELECT id, username, email, role, permissions, is_active FROM admin_users WHERE id = ?';
    const [user] = await db.execute(userQuery, [req.user.userId]);

    if (!user || !user.is_active) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found or inactive'
      });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      }
    });

  } catch (error) {
    console.error('Token verification error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred during token verification'
    });
  }
});

// Register new admin user (admin only)
router.post('/register', authenticateToken, requireAdmin, registerValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { username, email, password, role = 'moderator' } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Check if username or email already exists
    const existingUserQuery = 'SELECT id FROM admin_users WHERE username = ? OR email = ?';
    const [existingUser] = await db.execute(existingUserQuery, [username, email]);

    if (existingUser) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'Username or email already exists'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Insert new user
    const insertQuery = `
      INSERT INTO admin_users (username, password_hash, email, role, is_active, created_at)
      VALUES (?, ?, ?, ?, 1, NOW())
    `;
    
    const result = await db.execute(insertQuery, [username, passwordHash, email, role]);
    const newUserId = result.insertId;

    // Log user creation
    await logSecurityEvent(req.user.userId, 'user_created', clientIP, userAgent, {
      created_user_id: newUserId,
      created_username: username,
      created_role: role
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: {
        id: newUserId,
        username,
        email,
        role
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred during user registration'
    });
  }
});

// Change password endpoint
router.post('/change-password', authenticateToken, [
  body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Get current user
    const userQuery = 'SELECT password_hash FROM admin_users WHERE id = ?';
    const [user] = await db.execute(userQuery, [userId]);

    if (!user) {
      return res.status(404).json({
        error: 'User Not Found',
        message: 'User not found'
      });
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isValidPassword) {
      await logSecurityEvent(userId, 'password_change_failed', clientIP, userAgent, {
        reason: 'invalid_current_password'
      });

      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await db.execute(
      'UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [newPasswordHash, userId]
    );

    // Log password change
    await logSecurityEvent(userId, 'password_changed', clientIP, userAgent, {
      username: req.user.username
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while changing password'
    });
  }
});

// Helper function to log security events
async function logSecurityEvent(userId, action, ipAddress, userAgent, details = {}) {
  try {
    const query = `
      INSERT INTO admin_security_logs (user_id, action, ip_address, user_agent, details, created_at)
      VALUES (?, ?, ?, ?, ?, NOW())
    `;
    
    await db.execute(query, [
      userId,
      action,
      ipAddress,
      userAgent,
      JSON.stringify(details)
    ]);
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

// ============================================================================
// New Database-based Storage API Endpoints
// ============================================================================

// Middleware to get client info
const getClientInfo = (req) => ({
  userAgent: req.get('User-Agent'),
  ipAddress: req.ip || req.connection.remoteAddress
});

// Middleware to ensure session ID
const ensureSessionId = (req, res, next) => {
  if (!req.session.id) {
    req.session.id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
};

/**
 * Record login attempt
 * POST /api/auth/login-attempts
 */
router.post('/login-attempts', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const { success, username, failureReason } = req.body;
    const clientInfo = getClientInfo(req);

    await LoginAttemptsService.recordAttempt(
      sessionId,
      success,
      username,
      failureReason,
      clientInfo.userAgent,
      clientInfo.ipAddress
    );

    res.json({
      success: true,
      message: 'Login attempt recorded successfully'
    });
  } catch (error) {
    console.error('Error recording login attempt:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record login attempt'
    });
  }
});

/**
 * Get login attempts
 * GET /api/auth/login-attempts
 */
router.get('/login-attempts', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const hoursBack = parseInt(req.query.hours) || 1;
    const attempts = await LoginAttemptsService.getAttempts(sessionId, hoursBack);

    res.json({
      success: true,
      data: attempts
    });
  } catch (error) {
    console.error('Error getting login attempts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get login attempts'
    });
  }
});

/**
 * Clear login attempts
 * DELETE /api/auth/login-attempts
 */
router.delete('/login-attempts', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const count = await LoginAttemptsService.clearAttempts(sessionId);

    res.json({
      success: true,
      message: `Cleared ${count} login attempts`
    });
  } catch (error) {
    console.error('Error clearing login attempts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear login attempts'
    });
  }
});

/**
 * Check account lock status
 * GET /api/auth/account-status
 */
router.get('/account-status', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
    const lockoutDuration = parseInt(process.env.LOCKOUT_DURATION) || 15 * 60 * 1000;

    const isLocked = await LoginAttemptsService.isAccountLocked(sessionId, maxAttempts, lockoutDuration);

    res.json({
      success: true,
      data: { isLocked }
    });
  } catch (error) {
    console.error('Error checking account status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check account status'
    });
  }
});

/**
 * Get unlock time
 * GET /api/auth/unlock-time
 */
router.get('/unlock-time', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const attempts = await LoginAttemptsService.getAttempts(sessionId, 1);
    const failedAttempts = attempts.filter(attempt => !attempt.success);

    if (failedAttempts.length === 0) {
      return res.json({
        success: true,
        data: { unlockTime: 0 }
      });
    }

    const lockoutDuration = parseInt(process.env.LOCKOUT_DURATION) || 15 * 60 * 1000;
    const lastFailedAttempt = failedAttempts[failedAttempts.length - 1];
    const unlockTime = lastFailedAttempt.timestamp + lockoutDuration;

    res.json({
      success: true,
      data: { unlockTime }
    });
  } catch (error) {
    console.error('Error getting unlock time:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get unlock time'
    });
  }
});

/**
 * Log security event
 * POST /api/auth/security-logs
 */
router.post('/security-logs', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const { event, severity, details } = req.body;
    const clientInfo = getClientInfo(req);

    await SecurityLogsService.logEvent(
      sessionId,
      event,
      severity || 'medium',
      details,
      req.user?.id, // User ID if authenticated
      clientInfo.userAgent,
      clientInfo.ipAddress
    );

    res.json({
      success: true,
      message: 'Security event logged successfully'
    });
  } catch (error) {
    console.error('Error logging security event:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to log security event'
    });
  }
});

/**
 * Get security logs
 * GET /api/auth/security-logs
 */
router.get('/security-logs', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const limit = parseInt(req.query.limit) || 100;
    const logs = await SecurityLogsService.getLogs(sessionId, limit);

    res.json({
      success: true,
      data: logs
    });
  } catch (error) {
    console.error('Error getting security logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get security logs'
    });
  }
});

/**
 * Clear security logs
 * DELETE /api/auth/security-logs
 */
router.delete('/security-logs', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const count = await SecurityLogsService.clearLogs(sessionId);

    res.json({
      success: true,
      message: `Cleared ${count} security logs`
    });
  } catch (error) {
    console.error('Error clearing security logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear security logs'
    });
  }
});

/**
 * Create a new session
 * POST /api/auth/sessions
 */
router.post('/sessions', async (req, res) => {
  try {
    const { sessionData, expiresAt } = req.body;

    if (!sessionData || !expiresAt) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Session data and expiration time are required'
      });
    }

    // Generate unique session ID
    const sessionId = require('crypto').randomBytes(32).toString('hex');
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Store session in database
    await db.execute(
      'INSERT INTO user_sessions (id, user_id, session_data, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?, ?)',
      [sessionId, sessionData.user?.id || null, JSON.stringify(sessionData), clientIP, userAgent, expiresAt]
    );

    res.json({
      success: true,
      sessionId,
      message: 'Session created successfully'
    });
  } catch (error) {
    console.error('Session creation error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create session'
    });
  }
});

/**
 * Get session data
 * GET /api/auth/sessions/:sessionId
 */
router.get('/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Session ID is required'
      });
    }

    // Get session from database
    const [session] = await db.execute(
      'SELECT * FROM user_sessions WHERE id = ? AND expires_at > NOW()',
      [sessionId]
    );

    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Session not found or expired'
      });
    }

    // Parse session data
    let sessionData;
    try {
      sessionData = JSON.parse(session.session_data);
    } catch (error) {
      console.error('Failed to parse session data:', error);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Invalid session data'
      });
    }

    res.json({
      success: true,
      sessionData,
      expiresAt: session.expires_at
    });
  } catch (error) {
    console.error('Session retrieval error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve session'
    });
  }
});

/**
 * Delete session
 * DELETE /api/auth/sessions/:sessionId
 */
router.delete('/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Session ID is required'
      });
    }

    // Delete session from database
    const result = await db.execute(
      'DELETE FROM user_sessions WHERE id = ?',
      [sessionId]
    );

    res.json({
      success: true,
      message: 'Session deleted successfully',
      deleted: result.affectedRows > 0
    });
  } catch (error) {
    console.error('Session deletion error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete session'
    });
  }
});

module.exports = router;
