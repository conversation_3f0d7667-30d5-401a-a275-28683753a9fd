/**
 * Login Page Component
 * Secure login interface with form validation and error handling
 */

import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  EyeOff, 
  Lock, 
  User, 
  Shield, 
  ArrowLeft, 
  AlertTriangle,
  Clock,
  Loader2 
} from 'lucide-react';
import { LoginAttemptTracker } from '@/utils/authUtils';
import { scrollToTop } from '@/utils/scrollToTop';

export default function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { authState, login, checkAuthStatus } = useAuth();

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [accountLocked, setAccountLocked] = useState(false);
  const [lockoutTimeLeft, setLockoutTimeLeft] = useState(0);

  // Get redirect path from location state or default to admin panel
  const redirectTo = (location.state as any)?.from?.pathname || '/admin';

  // Check if user is already authenticated
  useEffect(() => {
    if (checkAuthStatus()) {
      navigate(redirectTo, { replace: true });
    }
  }, [checkAuthStatus, navigate, redirectTo]);

  // Check account lockout status
  useEffect(() => {
    const checkLockoutStatus = async () => {
      try {
        const isLocked = await LoginAttemptTracker.isAccountLocked();
        setAccountLocked(isLocked);

        if (isLocked) {
          const timeLeft = await LoginAttemptTracker.getTimeUntilUnlock();
          setLockoutTimeLeft(timeLeft);
        }
      } catch (error) {
        console.error('Error checking lockout status:', error);
        setAccountLocked(false);
        setLockoutTimeLeft(0);
      }
    };

    checkLockoutStatus();

    // Update lockout status every second if account is locked
    const interval = setInterval(async () => {
      if (accountLocked) {
        try {
          const timeLeft = await LoginAttemptTracker.getTimeUntilUnlock();
          setLockoutTimeLeft(timeLeft);

          if (timeLeft <= 0) {
            setAccountLocked(false);
            setLockoutTimeLeft(0);
          }
        } catch (error) {
          console.error('Error updating lockout time:', error);
          setAccountLocked(false);
          setLockoutTimeLeft(0);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [accountLocked]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (accountLocked) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await login(formData);
      
      if (result.success) {
        // Redirect to intended page or admin panel
        navigate(redirectTo, { replace: true });
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format lockout time
  const formatLockoutTime = (milliseconds: number): string => {
    const minutes = Math.ceil(milliseconds / (60 * 1000));
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header with back button */}
      <div className="w-full p-4 border-b border-border">
        <div className="max-w-md mx-auto flex items-center justify-between">
          <Link 
            to="/" 
            onClick={scrollToTop}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="text-sm">Back to Home</span>
          </Link>
          <Badge variant="outline" className="flex items-center gap-1">
            <Shield className="w-3 h-3" />
            <span className="text-xs">Secure Login</span>
          </Badge>
        </div>
      </div>

      {/* Main login content */}
      <div className="flex-1 flex items-center justify-center p-3 sm:p-4">
        <Card className="w-full max-w-md mx-2 sm:mx-0">
          <CardHeader className="text-center px-4 sm:px-6">
            <div className="mx-auto mb-4 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Lock className="w-6 h-6 text-primary" />
            </div>
            <CardTitle className="text-xl sm:text-2xl font-bold">Admin Login</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Enter your credentials to access the Admin Panel
            </CardDescription>
          </CardHeader>

          <CardContent className="px-4 sm:px-6">
            {/* Account locked alert */}
            {accountLocked && (
              <Alert className="mb-4 sm:mb-6 border-destructive/50 bg-destructive/10">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <AlertDescription className="text-destructive">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="w-4 h-4 flex-shrink-0" />
                    <span>
                      Account locked. Try again in {formatLockoutTime(lockoutTimeLeft)}.
                    </span>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Login error */}
            {authState.error && (
              <Alert className="mb-4 sm:mb-6 border-destructive/50 bg-destructive/10">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <AlertDescription className="text-destructive text-sm">
                  {authState.error}
                </AlertDescription>
              </Alert>
            )}



            {/* Login form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Username field */}
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    placeholder="Enter your username"
                    value={formData.username}
                    onChange={handleInputChange}
                    disabled={isSubmitting || accountLocked}
                    className="pl-10 min-h-[44px] text-base"
                    required
                  />
                </div>
              </div>

              {/* Password field */}
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange}
                    disabled={isSubmitting || accountLocked}
                    className="pl-10 pr-10 min-h-[44px] text-base"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isSubmitting || accountLocked}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* Submit button */}
              <Button
                type="submit"
                className="w-full min-h-[48px] text-base"
                disabled={isSubmitting || accountLocked || !formData.username || !formData.password}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <Lock className="w-4 h-4 mr-2" />
                    Sign In
                  </>
                )}
              </Button>
            </form>

            {/* Security notice */}
            <div className="mt-6 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-start gap-2">
                <Shield className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium mb-1">Security Notice:</p>
                  <ul className="space-y-1">
                    <li>• Sessions expire after 24 hours</li>
                    <li>• Account locks after 5 failed attempts</li>
                    <li>• All login attempts are logged</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="w-full p-4 border-t border-border">
        <div className="max-w-md mx-auto text-center">
          <p className="text-xs text-muted-foreground">
            StreamDB Admin Panel • Secure Authentication System
          </p>
        </div>
      </div>
    </div>
  );
}
