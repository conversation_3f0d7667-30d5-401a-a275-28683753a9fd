/**
 * Authentication Context
 * Provides authentication state management across the application
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import {
  AuthState,
  AuthContextType,
  LoginCredentials,
  LoginResponse,
  AuthUser,
  SessionData,
  PERMISSIONS
} from '@/types/auth';
import {
  LoginAttemptTracker,
  AuthValidator,
  SecurityLogger,
  generateSecureToken
} from '@/utils/authUtils';
import { SecureSessionManager } from '@/utils/sessionSecurity';
import { AUTH_CONFIG, DEMO_CREDENTIALS } from '@/config/auth';

// Authentication actions
type AuthAction = 
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: AuthUser; sessionExpiry: number } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'SESSION_RESTORED'; payload: { user: AuthUser; sessionExpiry: number } }
  | { type: 'SESSION_EXPIRED' }
  | { type: 'CLEAR_ERROR' };

// Initial authentication state
const initialAuthState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
  sessionExpiry: null,
};

// Authentication reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        sessionExpiry: action.payload.sessionExpiry,
        isLoading: false,
        error: null,
      };

    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        sessionExpiry: null,
        isLoading: false,
        error: action.payload,
      };

    case 'LOGOUT':
    case 'SESSION_EXPIRED':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        sessionExpiry: null,
        isLoading: false,
        error: null,
      };

    case 'SESSION_RESTORED':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        sessionExpiry: action.payload.sessionExpiry,
        isLoading: false,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
}

// Create authentication context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Demo credentials are now imported from config

/**
 * Authentication Provider Component
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, dispatch] = useReducer(authReducer, initialAuthState);

  /**
   * Restore session on app load
   */
  useEffect(() => {
    const restoreSession = () => {
      const sessionData = SecureSessionManager.getSession();
      if (sessionData && Date.now() < sessionData.expiresAt) {
        dispatch({
          type: 'SESSION_RESTORED',
          payload: {
            user: sessionData.user,
            sessionExpiry: sessionData.expiresAt,
          },
        });
        SecurityLogger.logEvent('SESSION_RESTORED', { userId: sessionData.user.id });
      }
    };

    restoreSession();
  }, []);

  /**
   * Session expiry monitoring
   */
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.sessionExpiry) return;

    const checkSessionExpiry = () => {
      if (Date.now() >= authState.sessionExpiry!) {
        handleSessionExpiry();
      }
    };

    // Check every minute
    const interval = setInterval(checkSessionExpiry, 60000);

    return () => clearInterval(interval);
  }, [authState.isAuthenticated, authState.sessionExpiry]);

  /**
   * Handle session expiry
   */
  const handleSessionExpiry = useCallback(() => {
    dispatch({ type: 'SESSION_EXPIRED' });
    SecureSessionManager.destroySession();
    SecurityLogger.logEvent('SESSION_EXPIRED', {}, 'medium');
  }, []);

  /**
   * Login function
   */
  const login = useCallback(async (credentials: LoginCredentials): Promise<LoginResponse> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // Check if account is locked
      if (LoginAttemptTracker.isAccountLocked()) {
        const timeUntilUnlock = LoginAttemptTracker.getTimeUntilUnlock();
        const minutesLeft = Math.ceil(timeUntilUnlock / (60 * 1000));
        
        SecurityLogger.logEvent('LOGIN_FAILED', { reason: 'ACCOUNT_LOCKED' }, 'high');
        
        const error = `Account locked due to too many failed attempts. Try again in ${minutesLeft} minutes.`;
        dispatch({ type: 'LOGIN_FAILURE', payload: error });
        
        return { success: false, error };
      }

      // Validate credentials
      const validation = AuthValidator.validateCredentials(credentials);
      if (!validation.valid) {
        dispatch({ type: 'LOGIN_FAILURE', payload: validation.error! });
        return { success: false, error: validation.error };
      }

      // Production authentication - use server API
      if (import.meta.env.PROD || !DEMO_CREDENTIALS) {
        try {
          // Use actual server-side authentication
          const apiService = await import('@/services/apiService');
          const response = await apiService.default.login(credentials);

          if (response?.success && response?.user) {
            LoginAttemptTracker.recordAttempt(true);
            LoginAttemptTracker.clearAttempts();

            const user: AuthUser = {
              id: response.user.id || generateSecureToken(),
              username: response.user.username || credentials.username,
              role: response.user.role || 'admin',
              lastLogin: new Date().toISOString(),
              permissions: response.user.permissions || [
                PERMISSIONS.ADMIN_PANEL_ACCESS,
                PERMISSIONS.CONTENT_CREATE,
                PERMISSIONS.CONTENT_EDIT,
                PERMISSIONS.CONTENT_DELETE,
                PERMISSIONS.BULK_OPERATIONS,
                PERMISSIONS.EXPORT_DATA,
              ],
            };

            const expiresAt = Date.now() + AUTH_CONFIG.sessionTimeout;
            const sessionData: SessionData = {
              user,
              token: response.token || generateSecureToken(),
              expiresAt,
              createdAt: Date.now(),
            };

            await SecureSessionManager.storeSession(sessionData);
            dispatch({ type: 'LOGIN_SUCCESS', payload: { user, sessionExpiry: expiresAt } });
            SecurityLogger.logEvent('LOGIN_SUCCESS', { username: credentials.username }, 'low');

            return { success: true, user, token: response.token };
          } else {
            throw new Error(response.error || 'Authentication failed');
          }
        } catch (error) {
          LoginAttemptTracker.recordAttempt(false);
          SecurityLogger.logEvent('LOGIN_FAILED', { username: credentials.username }, 'medium');

          const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
          dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });

          return { success: false, error: errorMessage };
        }
      }

      // Development authentication (demo credentials only in dev mode)
      const isValidCredentials = DEMO_CREDENTIALS &&
        credentials.username === DEMO_CREDENTIALS.username &&
        credentials.password === DEMO_CREDENTIALS.password;

      if (!isValidCredentials) {
        LoginAttemptTracker.recordAttempt(false);
        SecurityLogger.logEvent('LOGIN_FAILED', { username: credentials.username }, 'medium');

        const error = 'Invalid username or password';
        dispatch({ type: 'LOGIN_FAILURE', payload: error });

        return { success: false, error };
      }

      // Successful login
      LoginAttemptTracker.recordAttempt(true);
      LoginAttemptTracker.clearAttempts(); // Clear failed attempts on successful login

      const user: AuthUser = {
        id: generateSecureToken(),
        username: credentials.username,
        role: 'admin',
        lastLogin: new Date().toISOString(),
        permissions: [
          PERMISSIONS.ADMIN_PANEL_ACCESS,
          PERMISSIONS.CONTENT_CREATE,
          PERMISSIONS.CONTENT_EDIT,
          PERMISSIONS.CONTENT_DELETE,
          PERMISSIONS.BULK_OPERATIONS,
          PERMISSIONS.EXPORT_DATA,
        ],
      };

      const expiresAt = Date.now() + AUTH_CONFIG.sessionTimeout;
      const sessionData: SessionData = {
        user,
        token: generateSecureToken(),
        expiresAt,
        createdAt: Date.now(),
      };

      // Store session securely
      await SecureSessionManager.createSession(sessionData);

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user,
          sessionExpiry: expiresAt,
        },
      });

      SecurityLogger.logEvent('LOGIN_SUCCESS', { userId: user.id });

      return { success: true, user, expiresIn: AUTH_CONFIG.sessionTimeout };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      SecurityLogger.logEvent('LOGIN_FAILED', { error: errorMessage }, 'high');
      
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  }, []);

  /**
   * Logout function
   */
  const logout = useCallback(async () => {
    const userId = authState.user?.id;

    dispatch({ type: 'LOGOUT' });
    await SecureSessionManager.destroySession();

    SecurityLogger.logEvent('LOGOUT', { userId });
  }, [authState.user?.id]);

  /**
   * Refresh session
   */
  const refreshSession = useCallback(async (): Promise<boolean> => {
    const sessionData = await SecureSessionManager.getSession();
    if (!sessionData) return false;

    // Extend session if it's still valid and close to expiry
    const timeUntilExpiry = sessionData.expiresAt - Date.now();
    if (timeUntilExpiry > 0 && timeUntilExpiry < AUTH_CONFIG.tokenRefreshThreshold) {
      const newExpiresAt = Date.now() + AUTH_CONFIG.sessionTimeout;
      const updatedSessionData: SessionData = {
        ...sessionData,
        expiresAt: newExpiresAt,
      };

      SecureSessionManager.createSession(updatedSessionData);

      dispatch({
        type: 'SESSION_RESTORED',
        payload: {
          user: sessionData.user,
          sessionExpiry: newExpiresAt,
        },
      });

      SecurityLogger.logEvent('SESSION_REFRESHED', { userId: sessionData.user.id });
      return true;
    }

    return false;
  }, []);

  /**
   * Check authentication status
   */
  const checkAuthStatus = useCallback(async (): Promise<boolean> => {
    const sessionData = await SecureSessionManager.getSession();
    return authState.isAuthenticated && sessionData !== null;
  }, [authState.isAuthenticated]);

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback((permission: string): boolean => {
    return authState.user?.permissions.includes(permission) ?? false;
  }, [authState.user?.permissions]);

  /**
   * Check if session is valid
   */
  const isSessionValid = useCallback((): boolean => {
    return SecureSessionManager.getSession() !== null;
  }, []);

  /**
   * Get time until session expiry
   */
  const getTimeUntilExpiry = useCallback((): number => {
    const stats = SecureSessionManager.getSessionStats();
    return stats.timeRemaining;
  }, []);

  const contextValue: AuthContextType = {
    authState,
    login,
    logout,
    refreshSession,
    checkAuthStatus,
    hasPermission,
    isSessionValid,
    getTimeUntilExpiry,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
