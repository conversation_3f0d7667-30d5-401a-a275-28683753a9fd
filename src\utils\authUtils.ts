/**
 * Authentication Utilities
 * Secure client-side authentication logic with protection against common vulnerabilities
 */

import {
  LoginCredentials,
  AuthUser,
  SessionData,
  LoginAttempt,
  AuthError,
  AuthEventLog,
  AuthEvent
} from '@/types/auth';
import { AUTH_CONFIG, SECURITY_CONFIG, ENCRYPTION_KEY } from '@/config/auth';
const SESSION_STORAGE_KEY = 'streamdb_auth_session';
const LOGIN_ATTEMPTS_KEY = 'streamdb_login_attempts';

// API Configuration for database operations
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'  // Production: same domain
  : 'http://localhost:3001/api';  // Development: local server

// Production mode check
const IS_PRODUCTION = process.env.NODE_ENV === 'production' || !import.meta.env.DEV;

// Safe database API wrapper to handle connection failures
const safeDatabaseAPI = {
  async get(endpoint: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'GET',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Database API GET failed:', error);
      return null;
    }
  },

  async post(endpoint: string, data: any): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Database API POST failed:', error);
      return null;
    }
  },

  async delete(endpoint: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'DELETE',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.warn('Database API DELETE failed:', error);
      return false;
    }
  }
};

/**
 * Simple XOR encryption for session data
 * Note: This is basic client-side obfuscation. Real security comes from server-side validation.
 */
function encryptData(data: string): string {
  let encrypted = '';
  for (let i = 0; i < data.length; i++) {
    encrypted += String.fromCharCode(
      data.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length)
    );
  }
  return btoa(encrypted); // Base64 encode
}

function decryptData(encryptedData: string): string {
  try {
    const encrypted = atob(encryptedData); // Base64 decode
    let decrypted = '';
    for (let i = 0; i < encrypted.length; i++) {
      decrypted += String.fromCharCode(
        encrypted.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length)
      );
    }
    return decrypted;
  } catch (error) {
    console.error('Failed to decrypt session data:', error);
    return '';
  }
}

/**
 * Secure session storage utilities
 */
export class SecureSessionStorage {
  /**
   * Store session data securely
   */
  static setSession(sessionData: SessionData): void {
    try {
      const dataString = JSON.stringify(sessionData);
      const encryptedData = encryptData(dataString);
      sessionStorage.setItem(SESSION_STORAGE_KEY, encryptedData);
    } catch (error) {
      console.error('Failed to store session data:', error);
    }
  }

  /**
   * Retrieve session data securely
   */
  static getSession(): SessionData | null {
    try {
      const encryptedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
      if (!encryptedData) return null;

      const decryptedData = decryptData(encryptedData);
      if (!decryptedData) return null;

      const sessionData: SessionData = JSON.parse(decryptedData);
      
      // Check if session is expired
      if (Date.now() > sessionData.expiresAt) {
        this.clearSession();
        return null;
      }

      return sessionData;
    } catch (error) {
      console.error('Failed to retrieve session data:', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * Clear session data
   */
  static clearSession(): void {
    sessionStorage.removeItem(SESSION_STORAGE_KEY);
  }

  /**
   * Check if session exists and is valid
   */
  static isSessionValid(): boolean {
    const session = this.getSession();
    return session !== null && Date.now() < session.expiresAt;
  }

  /**
   * Get time until session expiry in milliseconds
   */
  static getTimeUntilExpiry(): number {
    const session = this.getSession();
    if (!session) return 0;
    return Math.max(0, session.expiresAt - Date.now());
  }
}

/**
 * Production-Ready Login Attempt Tracking
 * Uses database in production, local storage only as development fallback
 */
export class LoginAttemptTracker {
  /**
   * Record a login attempt
   */
  static async recordAttempt(success: boolean, username?: string, failureReason?: string): Promise<void> {
    try {
      // In production, use database exclusively
      if (IS_PRODUCTION) {
        const response = await safeDatabaseAPI.post('/auth/login-attempts', {
          success,
          username,
          failureReason,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        });

        if (!response || !response.success) {
          console.error('Failed to record login attempt to database in production');
          throw new Error('Database login attempt recording failed');
        }
      } else {
        // Development: try database first, fallback to local storage
        try {
          const response = await safeDatabaseAPI.post('/auth/login-attempts', {
            success,
            username,
            failureReason,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
          });

          if (!response || !response.success) {
            console.warn('Database unavailable in development, using local storage fallback');
            this.storeLocalAttempt(success);
          }
        } catch (error) {
          console.warn('Database unavailable in development, using local storage fallback:', error);
          this.storeLocalAttempt(success);
        }
      }
    } catch (error) {
      if (IS_PRODUCTION) {
        console.error('Critical: Login attempt tracking failed in production:', error);
        // In production, this is a critical error - don't allow silent failures
        throw error;
      } else {
        console.error('Login attempt tracking failed, using local storage fallback:', error);
        this.storeLocalAttempt(success);
      }
    }
  }

  /**
   * Get recent login attempts
   */
  static async getAttempts(): Promise<LoginAttempt[]> {
    try {
      const response = await safeDatabaseAPI.get('/auth/login-attempts');
      if (!response || !response.success) {
        return [];
      }

      return response.data.map((attempt: any) => ({
        timestamp: attempt.timestamp,
        success: attempt.success,
        userAgent: attempt.user_agent,
        ip: attempt.ip_address
      }));
    } catch (error) {
      console.error('Failed to retrieve login attempts:', error);
      return [];
    }
  }

  /**
   * Check if account should be locked due to too many failed attempts
   * Production: Database-only, Development: Database with local fallback
   */
  static async isAccountLocked(): Promise<boolean> {
    try {
      const response = await safeDatabaseAPI.get('/auth/account-status');

      if (!response || !response.success) {
        if (IS_PRODUCTION) {
          console.error('Critical: Account lock status check failed in production');
          // In production, assume not locked if database fails (fail-open for availability)
          // but log this as a critical security event
          SecurityLogger.logEvent('SECURITY_VIOLATION', {
            reason: 'Account lock status check failed',
            severity: 'critical'
          }, 'critical');
          return false;
        } else {
          // Development: fallback to local storage
          console.warn('Database unavailable in development, using local storage fallback');
          return this.isAccountLockedLocal();
        }
      }

      return response.data.isLocked || false;
    } catch (error) {
      console.error('Failed to check account lock status:', error);

      if (IS_PRODUCTION) {
        // Production: log critical error but don't block access
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Account lock status check error',
          error: error instanceof Error ? error.message : 'Unknown error',
          severity: 'critical'
        }, 'critical');
        return false;
      } else {
        // Development: fallback to local storage
        return this.isAccountLockedLocal();
      }
    }
  }

  /**
   * Get time until account unlock in milliseconds
   * Production: Database-only, Development: Database with local fallback
   */
  static async getTimeUntilUnlock(): Promise<number> {
    try {
      const isLocked = await this.isAccountLocked();
      if (!isLocked) return 0;

      const response = await safeDatabaseAPI.get('/auth/unlock-time');

      if (!response || !response.success) {
        if (IS_PRODUCTION) {
          console.error('Critical: Unlock time check failed in production');
          // In production, return 0 if database fails (fail-open)
          SecurityLogger.logEvent('SECURITY_VIOLATION', {
            reason: 'Unlock time check failed',
            severity: 'critical'
          }, 'critical');
          return 0;
        } else {
          // Development: fallback to local storage
          console.warn('Database unavailable in development, using local storage fallback');
          return this.getTimeUntilUnlockLocal();
        }
      }

      const unlockTime = response.data.unlockTime;
      if (typeof unlockTime !== 'number' || isNaN(unlockTime)) {
        console.warn('Invalid unlock time received from server:', unlockTime);

        if (IS_PRODUCTION) {
          SecurityLogger.logEvent('SECURITY_VIOLATION', {
            reason: 'Invalid unlock time from database',
            unlockTime,
            severity: 'high'
          }, 'high');
          return 0;
        } else {
          return this.getTimeUntilUnlockLocal();
        }
      }

      return Math.max(0, unlockTime - Date.now());
    } catch (error) {
      console.error('Failed to get unlock time:', error);

      if (IS_PRODUCTION) {
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Unlock time check error',
          error: error instanceof Error ? error.message : 'Unknown error',
          severity: 'critical'
        }, 'critical');
        return 0;
      } else {
        return this.getTimeUntilUnlockLocal();
      }
    }
  }

  /**
   * Local fallback for account lock check (client-side only)
   */
  private static isAccountLockedLocal(): boolean {
    try {
      const attempts = this.getLocalAttempts();
      const recentFailures = attempts.filter(attempt =>
        !attempt.success &&
        (Date.now() - attempt.timestamp) < AUTH_CONFIG.lockoutDuration
      );

      if (recentFailures.length >= AUTH_CONFIG.maxLoginAttempts) {
        const lastFailure = Math.max(...recentFailures.map(a => a.timestamp));
        const lockoutEnd = lastFailure + AUTH_CONFIG.lockoutDuration;
        return Date.now() < lockoutEnd;
      }

      return false;
    } catch (error) {
      console.error('Error checking local account lock status:', error);
      return false;
    }
  }

  /**
   * Local fallback for unlock time calculation (client-side only)
   */
  private static getTimeUntilUnlockLocal(): number {
    try {
      const attempts = this.getLocalAttempts();
      const recentFailures = attempts.filter(attempt =>
        !attempt.success &&
        (Date.now() - attempt.timestamp) < AUTH_CONFIG.lockoutDuration
      );

      if (recentFailures.length >= AUTH_CONFIG.maxLoginAttempts) {
        const lastFailure = Math.max(...recentFailures.map(a => a.timestamp));
        const lockoutEnd = lastFailure + AUTH_CONFIG.lockoutDuration;
        return Math.max(0, lockoutEnd - Date.now());
      }

      return 0;
    } catch (error) {
      console.error('Error calculating local unlock time:', error);
      return 0;
    }
  }

  /**
   * Get local login attempts from localStorage
   */
  private static getLocalAttempts(): LoginAttempt[] {
    try {
      const stored = localStorage.getItem(LOGIN_ATTEMPTS_KEY);
      if (!stored) return [];

      const attempts = JSON.parse(stored);
      return Array.isArray(attempts) ? attempts : [];
    } catch (error) {
      console.error('Error reading local login attempts:', error);
      return [];
    }
  }

  /**
   * Store login attempt locally
   */
  private static storeLocalAttempt(success: boolean): void {
    try {
      const attempts = this.getLocalAttempts();
      const newAttempt: LoginAttempt = {
        timestamp: Date.now(),
        success,
        userAgent: navigator.userAgent,
        ip: 'local'
      };

      // Keep only recent attempts (last 24 hours)
      const cutoff = Date.now() - (24 * 60 * 60 * 1000);
      const recentAttempts = attempts.filter(a => a.timestamp > cutoff);
      recentAttempts.push(newAttempt);

      localStorage.setItem(LOGIN_ATTEMPTS_KEY, JSON.stringify(recentAttempts));
    } catch (error) {
      console.error('Error storing local login attempt:', error);
    }
  }

  /**
   * Clear login attempts (used after successful login)
   * Production: Database-only, Development: Database with local fallback
   */
  static async clearAttempts(): Promise<void> {
    try {
      // In production, use database exclusively
      if (IS_PRODUCTION) {
        const response = await safeDatabaseAPI.delete('/auth/login-attempts');
        if (!response) {
          console.error('Critical: Failed to clear login attempts from database in production');
          throw new Error('Database login attempt clearing failed');
        }
      } else {
        // Development: try database first, then clear local storage
        try {
          const response = await safeDatabaseAPI.delete('/auth/login-attempts');
          if (!response) {
            console.warn('Database unavailable in development, clearing local storage');
          }
        } catch (error) {
          console.warn('Database unavailable in development:', error);
        }

        // Always clear local storage in development
        try {
          localStorage.removeItem(LOGIN_ATTEMPTS_KEY);
        } catch (error) {
          console.error('Failed to clear local login attempts:', error);
        }
      }
    } catch (error) {
      if (IS_PRODUCTION) {
        console.error('Critical: Login attempt clearing failed in production:', error);
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Failed to clear login attempts',
          error: error instanceof Error ? error.message : 'Unknown error',
          severity: 'high'
        }, 'high');
        throw error;
      } else {
        console.error('Login attempt clearing failed, clearing local storage:', error);
        try {
          localStorage.removeItem(LOGIN_ATTEMPTS_KEY);
        } catch (localError) {
          console.error('Failed to clear local login attempts:', localError);
        }
      }
    }
  }
}

/**
 * Input validation and sanitization
 */
export class AuthValidator {
  /**
   * Validate login credentials
   */
  static validateCredentials(credentials: LoginCredentials): { valid: boolean; error?: string } {
    if (!credentials.username || !credentials.password) {
      return { valid: false, error: 'Username and password are required' };
    }

    if (credentials.username.length < 3) {
      return { valid: false, error: 'Username must be at least 3 characters long' };
    }

    if (credentials.password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters long' };
    }

    // Check for potential XSS attempts
    if (this.containsXSS(credentials.username) || this.containsXSS(credentials.password)) {
      return { valid: false, error: 'Invalid characters detected' };
    }

    return { valid: true };
  }

  /**
   * Basic XSS detection
   */
  private static containsXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe/gi,
      /<object/gi,
      /<embed/gi,
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize input string
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
}

/**
 * Production-Ready Security Event Logging
 * Uses database in production, console logging as fallback
 */
export class SecurityLogger {
  /**
   * Log a security event
   * Production: Database-only with critical error handling
   * Development: Database with console fallback
   */
  static async logEvent(event: AuthEvent, details?: Record<string, any>, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'): Promise<void> {
    const logData = {
      event,
      timestamp: Date.now(),
      details,
      severity,
      userAgent: navigator.userAgent
    };

    try {
      // In production, security logging is critical
      if (IS_PRODUCTION) {
        const response = await safeDatabaseAPI.post('/auth/security-logs', logData);

        if (!response || !response.success) {
          console.error('Critical: Security event logging failed in production', logData);
          // In production, security logging failures are critical
          // But don't throw to avoid breaking user experience
        }
      } else {
        // Development: try database, fallback to console
        try {
          const response = await safeDatabaseAPI.post('/auth/security-logs', logData);

          if (!response || !response.success) {
            console.warn('Database unavailable in development, logging to console:', logData);
          }
        } catch (error) {
          console.warn('Database unavailable in development, logging to console:', logData);
          console.warn('Database error:', error);
        }
      }
    } catch (error) {
      if (IS_PRODUCTION) {
        console.error('Critical: Security event logging error in production:', error, logData);
      } else {
        console.error('Security event logging failed, logging to console:', error, logData);
      }
    }
  }

  /**
   * Get security logs
   */
  static async getLogs(limit: number = 100): Promise<AuthEventLog[]> {
    try {
      const response = await safeDatabaseAPI.get(`/auth/security-logs?limit=${limit}`);
      if (!response || !response.success) {
        return [];
      }

      return response.data.map((log: any) => ({
        event: log.event_type,
        timestamp: log.timestamp,
        details: log.details ? JSON.parse(log.details) : undefined,
        severity: log.severity,
        userId: log.user_id
      }));
    } catch (error) {
      console.error('Failed to retrieve security logs:', error);
      return [];
    }
  }

  /**
   * Clear security logs
   */
  static async clearLogs(): Promise<void> {
    try {
      const response = await safeDatabaseAPI.delete('/auth/security-logs');
      if (!response) {
        console.warn('Failed to clear security logs from database');
      }
    } catch (error) {
      console.error('Failed to clear security logs:', error);
    }
  }
}

/**
 * Generate secure random token
 */
export function generateSecureToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Hash password (basic client-side hashing - server should do proper hashing)
 */
export function hashPassword(password: string): string {
  // This is a simple hash for client-side use only
  // Real password hashing should be done server-side with proper salt and algorithms
  let hash = 0;
  for (let i = 0; i < password.length; i++) {
    const char = password.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(16);
}
